use std::net::Ipv4Addr;
use std::thread;
use std::time::{Duration, Instant};

use xdb::{search_by_ip, searcher_init};

fn main() {
    // 配置输出日志信息
    tracing_subscriber::fmt::init();

    // 初始化加载xdb文件
    let xdb_filepath = "./ip2region.xdb";
    searcher_init(Some(xdb_filepath.to_owned()));
    // 如果../data或者../../data或者../../../data下面有对应的ip2region.xdb文件
    // 初始化函数可以直接调用如下
    // searcher_init(None);

    println!("\n测试多类型查询");
    println!("{}", search_by_ip("9999999").unwrap());
    println!("{}", search_by_ip("*******").unwrap());
    println!("{}", search_by_ip(9999999).unwrap());
    println!("{:?}", search_by_ip(Ipv4Addr::from(3333333)));

    println!("\n测试多线程初始化以及多线程查询");
    for i in 1..5 {
        thread::spawn(move || {
            // 再次初始化是没什么效果的
            searcher_init(Some(xdb_filepath.to_owned()));
            println!("in thread {i} {:?}", search_by_ip(rand::random::<u32>()));
        });
    }
    // 等待多线程执行结束
    thread::sleep(Duration::from_secs(1));

    let count = 10_000;
    print!("\n计算一万数据总共耗时: ");
    let now = Instant::now();
    for _ in 0..count {
        search_by_ip(rand::random::<u32>()).unwrap();
    }
    println!("{:?}, ave: {:?}", now.elapsed(), now.elapsed()/count);

    print!("\n计算一万数据，每次迭代都统计耗时的总共耗时: ");
    let mut total = Duration::from_secs(0);
    for _ in 0..count {
        let current = Instant::now();
        search_by_ip(rand::random::<u32>()).unwrap();
        total += current.elapsed();
    }
    println!("{:?}, ave: {:?}", total, total/count);

    print!("\n计算空迭代耗时: ");
    let now = Instant::now();
    for _ in 0..count {}
    println!("{:?}", now.elapsed());
}
