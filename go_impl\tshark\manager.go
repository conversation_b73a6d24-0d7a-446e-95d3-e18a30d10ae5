package tshark

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"sync"

	"github.com/sirupsen/logrus"
	"xuanyuan/go_impl/models"
	"xuanyuan/go_impl/utils"
)

// TsharkManager TShark 管理器
type TsharkManager struct {
	// TShark 可执行文件路径
	tsharkPath string
	// 当前分析的文件路径
	currentFilePath string
	// 分析得到的所有数据包
	allPackets map[uint32]*models.Packet
	// 互斥锁
	mu sync.RWMutex
}

// NewTsharkManager 创建新的 TShark 管理器
func NewTsharkManager() *TsharkManager {
	return &TsharkManager{
		tsharkPath: "tshark", // 默认假设 tshark 在 PATH 中
		allPackets: make(map[uint32]*models.Packet),
	}
}

// SetTsharkPath 设置 TShark 路径
func (m *TsharkManager) SetTsharkPath(path string) {
	m.tsharkPath = path
}

// AnalysisFile 分析数据包文件
func (m *TsharkManager) AnalysisFile(filePath string) error {
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("file not found: %s", filePath)
	}

	// 构建 TShark 命令
	cmd := exec.Command(m.tsharkPath,
		"-r", filePath,
		"-T", "fields",
		"-e", "frame.number",
		"-e", "frame.time_epoch",
		"-e", "frame.len",
		"-e", "frame.cap_len",
		"-e", "eth.src",
		"-e", "eth.dst",
		"-e", "ip.src",
		"-e", "ipv6.src",
		"-e", "ip.dst",
		"-e", "ipv6.dst",
		"-e", "tcp.srcport",
		"-e", "udp.srcport",
		"-e", "tcp.dstport",
		"-e", "udp.dstport",
		"-e", "_ws.col.Protocol",
		"-e", "_ws.col.Info",
	)

	// 执行命令
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to get stdout pipe: %v", err)
	}

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start tshark command: %v", err)
	}

	// 当前处理的报文在文件中的偏移，第一个报文的偏移就是全局文件头24字节
	fileOffset := uint32(24) // sizeof(PcapHeader)

	// 清空之前的数据
	m.mu.Lock()
	m.allPackets = make(map[uint32]*models.Packet)
	m.mu.Unlock()

	// 处理每一行输出
	scanner := bufio.NewScanner(stdout)
	for scanner.Scan() {
		line := scanner.Text()

		// 解析行
		if packet := m.parseLine(line); packet != nil {
			// 计算当前报文的偏移，然后记录在 Packet 对象中
			packet.FileOffset = fileOffset + 16 // sizeof(PacketHeader)

			// 更新偏移游标
			fileOffset = fileOffset + 16 + packet.CapLen // sizeof(PacketHeader) + cap_len

			// 获取 IP 地理位置
			packet.SrcLocation = utils.GetIpLocation(packet.SrcIP)
			packet.DstLocation = utils.GetIpLocation(packet.DstIP)

			// 保存数据包
			m.mu.Lock()
			m.allPackets[packet.FrameNumber] = packet
			m.mu.Unlock()
		}
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading tshark output: %v", err)
	}

	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("tshark command failed: %v", err)
	}

	// 记录当前分析的文件路径
	m.currentFilePath = filePath

	logrus.Infof("Analysis completed. Total packets: %d", len(m.allPackets))
	return nil
}

// parseLine 解析 TShark 输出的一行
func (m *TsharkManager) parseLine(line string) *models.Packet {
	fields := strings.Split(line, "\t")

	if len(fields) < 16 {
		logrus.Debugf("Invalid line format: %s", line)
		return nil
	}

	packet := models.NewPacket()

	// 解析字段
	frameNumber, err := strconv.ParseUint(fields[0], 10, 32)
	if err == nil {
		packet.FrameNumber = uint32(frameNumber)
	}

	packet.Time = fields[1]

	length, err := strconv.ParseUint(fields[2], 10, 32)
	if err == nil {
		packet.Len = uint32(length)
	}

	capLen, err := strconv.ParseUint(fields[3], 10, 32)
	if err == nil {
		packet.CapLen = uint32(capLen)
	}

	packet.SrcMAC = fields[4]
	packet.DstMAC = fields[5]

	// IP 地址可能是 IPv4 或 IPv6
	if fields[6] != "" {
		packet.SrcIP = fields[6]
	} else {
		packet.SrcIP = fields[7]
	}

	if fields[8] != "" {
		packet.DstIP = fields[8]
	} else {
		packet.DstIP = fields[9]
	}

	// 端口可能是 TCP 或 UDP
	if fields[10] != "" || fields[11] != "" {
		var srcPort uint64
		if fields[10] != "" {
			srcPort, _ = strconv.ParseUint(fields[10], 10, 16)
		} else {
			srcPort, _ = strconv.ParseUint(fields[11], 10, 16)
		}
		packet.SrcPort = uint16(srcPort)
	}

	if fields[12] != "" || fields[13] != "" {
		var dstPort uint64
		if fields[12] != "" {
			dstPort, _ = strconv.ParseUint(fields[12], 10, 16)
		} else {
			dstPort, _ = strconv.ParseUint(fields[13], 10, 16)
		}
		packet.DstPort = uint16(dstPort)
	}

	packet.Protocol = fields[14]
	packet.Info = fields[15]

	return packet
}

// GetPacketCount 获取数据包数量
func (m *TsharkManager) GetPacketCount() int {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return len(m.allPackets)
}

// GetPacket 获取指定编号的数据包
func (m *TsharkManager) GetPacket(frameNumber uint32) (*models.Packet, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	packet, ok := m.allPackets[frameNumber]
	return packet, ok
}

// GetAllPackets 获取所有数据包
func (m *TsharkManager) GetAllPackets() []*models.Packet {
	m.mu.RLock()
	defer m.mu.RUnlock()
	packets := make([]*models.Packet, 0, len(m.allPackets))
	for _, packet := range m.allPackets {
		packets = append(packets, packet)
	}
	return packets
}

// GetPacketHexData 获取指定编号数据包的十六进制数据
func (m *TsharkManager) GetPacketHexData(frameNumber uint32) ([]byte, error) {
	// 检查当前是否有分析的文件
	if m.currentFilePath == "" {
		return nil, fmt.Errorf("no file has been analyzed")
	}

	// 查找指定编号的数据包
	m.mu.RLock()
	packet, ok := m.allPackets[frameNumber]
	m.mu.RUnlock()

	if !ok {
		return nil, fmt.Errorf("packet not found: %d", frameNumber)
	}

	// 打开文件
	file, err := os.Open(m.currentFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %v", err)
	}
	defer file.Close()

	// 定位到数据包的位置
	_, err = file.Seek(int64(packet.FileOffset), io.SeekStart)
	if err != nil {
		return nil, fmt.Errorf("failed to seek to packet position: %v", err)
	}

	// 读取数据包内容
	buffer := make([]byte, packet.CapLen)
	_, err = io.ReadFull(file, buffer)
	if err != nil {
		return nil, fmt.Errorf("failed to read packet data: %v", err)
	}

	return buffer, nil
}

// GetNetworkAdapters 枚举网卡列表
func (m *TsharkManager) GetNetworkAdapters() ([]*models.AdapterInfo, error) {
	// 需要过滤掉的虚拟网卡
	specialInterfaces := map[string]bool{
		"sshdump":   true,
		"ciscodump": true,
		"udpdump":   true,
		"randpkt":   true,
	}

	// 执行 tshark -D 命令
	cmd := exec.Command(m.tsharkPath, "-D")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute tshark -D command: %v", err)
	}

	// 创建正则表达式模式
	pattern := regexp.MustCompile(`^\d+\.\s+(\S+)(?:.*?\(([^)]*)\))?.*$`)

	var adapters []*models.AdapterInfo
	index := 1

	// 处理每一行
	scanner := bufio.NewScanner(strings.NewReader(string(output)))
	for scanner.Scan() {
		line := scanner.Text()
		logrus.Debugf("Processing line: %s", line)

		matches := pattern.FindStringSubmatch(line)
		if len(matches) > 1 {
			interfaceName := matches[1]

			// 过滤特殊网卡
			if specialInterfaces[interfaceName] {
				continue
			}

			adapter := models.NewAdapterInfo()
			adapter.ID = index
			adapter.Name = interfaceName

			// 提取备注信息
			if len(matches) > 2 && matches[2] != "" {
				adapter.Remark = matches[2]
				logrus.Debugf("Found adapter: %s with remark: %s", adapter.Name, adapter.Remark)
			} else {
				logrus.Debugf("Found adapter: %s (no remark)", adapter.Name)
			}

			adapters = append(adapters, adapter)
			index++
		}
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("error reading tshark output: %v", err)
	}

	return adapters, nil
}

// Cleanup 清理资源
func (m *TsharkManager) Cleanup() {
	utils.UninitIP2Region()
}
