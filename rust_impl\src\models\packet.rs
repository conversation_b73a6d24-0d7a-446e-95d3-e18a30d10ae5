use serde::{Deserialize, Serialize};

/// 表示一个网络数据包
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Packet {
    /// 帧编号
    pub frame_number: u32,
    /// 时间戳
    pub time: String,
    /// 源 MAC 地址
    pub src_mac: String,
    /// 目标 MAC 地址
    pub dst_mac: String,
    /// 捕获长度
    pub cap_len: u32,
    /// 原始长度
    pub len: u32,
    /// 源 IP 地址
    pub src_ip: String,
    /// 源 IP 地理位置
    pub src_location: String,
    /// 源端口
    pub src_port: u16,
    /// 目标 IP 地址
    pub dst_ip: String,
    /// 目标 IP 地理位置
    pub dst_location: String,
    /// 目标端口
    pub dst_port: u16,
    /// 协议
    pub protocol: String,
    /// 信息
    pub info: String,
    /// 文件偏移
    pub file_offset: u32,
}

impl Default for Packet {
    fn default() -> Self {
        Self {
            frame_number: 0,
            time: String::new(),
            src_mac: String::new(),
            dst_mac: String::new(),
            cap_len: 0,
            len: 0,
            src_ip: String::new(),
            src_location: String::new(),
            src_port: 0,
            dst_ip: String::new(),
            dst_location: String::new(),
            dst_port: 0,
            protocol: String::new(),
            info: String::new(),
            file_offset: 0,
        }
    }
}

/// PCAP 文件头
#[derive(Debug, Clone)]
pub struct PcapHeader {
    pub magic_number: u32,
    pub version_major: u16,
    pub version_minor: u16,
    pub thiszone: i32,
    pub sigfigs: u32,
    pub snaplen: u32,
    pub network: u32,
}

/// 数据包头
#[derive(Debug, Clone)]
pub struct PacketHeader {
    pub ts_sec: u32,
    pub ts_usec: u32,
    pub caplen: u32,
    pub len: u32,
}

/// 网卡信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterInfo {
    /// 网卡 ID
    pub id: i32,
    /// 网卡名称
    pub name: String,
    /// 网卡备注
    pub remark: String,
}

impl Default for AdapterInfo {
    fn default() -> Self {
        Self {
            id: 0,
            name: String::new(),
            remark: String::new(),
        }
    }
}
