const fs = require('fs');
const path = require('path');
const os = require('os');
const { Command } = require('commander');
const winston = require('winston');
const TsharkManager = require('./tshark/manager');
const IP2Region = require('./utils/ip2region');

// 配置日志
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
    ),
    transports: [
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.timestamp(),
                winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
            )
        })
    ]
});

// 创建命令行程序
const program = new Command();

// 配置命令行选项
program
    .name('xuanyuan')
    .description('网络数据包分析工具 - Node.js 实现')
    .version('1.0.0');

// 分析数据包文件命令
program
    .command('analyze')
    .description('分析数据包文件')
    .requiredOption('-f, --file <path>', '数据包文件路径')
    .option('-t, --tshark-path <path>', 'TShark 可执行文件路径')
    .option('-i, --ip2region-path <path>', 'IP2Region 数据库路径', 'ip2region.xdb')
    .option('-l, --log-level <level>', '日志级别 (debug, info, warn, error)', 'info')
    .action(async (options) => {
        try {
            // 设置日志级别
            setLogLevel(options.logLevel);

            // 初始化 IP2Region
            try {
                await IP2Region.init(options.ip2regionPath);
            } catch (error) {
                logger.error(`Failed to initialize IP2Region: ${error.message}`);
                logger.error('IP 地理位置查询功能将不可用');
            }

            // 创建 TShark 管理器
            const manager = new TsharkManager();

            // 设置 TShark 路径
            if (options.tsharkPath) {
                manager.setTsharkPath(options.tsharkPath);
            } else if (os.platform() === 'win32') {
                manager.setTsharkPath('C:\\Program Files\\Wireshark\\tshark.exe');
            }

            // 分析数据包文件
            logger.info(`Analyzing file: ${options.file}`);

            await manager.analysisFile(options.file);
            printAllPackets(manager);
            // 打印数据包数量
            logger.info(`Total packets: ${manager.getPacketCount()}`);

            // 打印所有数据包
            // printAllPackets(manager);

            logger.info('Analysis completed successfully');

            // 清理资源
            manager.cleanup();
        } catch (error) {
            logger.error(`Error: ${error.message}`);
            process.exit(1);
        }
    });

// 列出网络适配器命令
program
    .command('list-adapters')
    .description('列出网络适配器')
    .option('-t, --tshark-path <path>', 'TShark 可执行文件路径')
    .option('-l, --log-level <level>', '日志级别 (debug, info, warn, error)', 'info')
    .action(async (options) => {
        try {
            // 设置日志级别
            setLogLevel(options.logLevel);

            // 创建 TShark 管理器
            const manager = new TsharkManager();

            // 设置 TShark 路径
            if (options.tsharkPath) {
                manager.setTsharkPath(options.tsharkPath);
            } else if (os.platform() === 'win32') {
                manager.setTsharkPath('C:\\Program Files\\Wireshark\\tshark.exe');
            }

            // 列出网络适配器
            logger.info('Listing network adapters...');

            const adapters = await manager.getNetworkAdapters();

            if (adapters.length === 0) {
                logger.info('No network adapters found');
            } else {
                for (const adapter of adapters) {
                    logger.info(`ID: ${adapter.id}, Name: ${adapter.name}, Description: ${adapter.remark}`);
                }
            }

            // 清理资源
            manager.cleanup();
        } catch (error) {
            logger.error(`Error: ${error.message}`);
            process.exit(1);
        }
    });

// 设置日志级别
function setLogLevel(level) {
    console.log(`Setting log level to: ${level}`);

    // 设置 winston 日志级别
    logger.level = level;

    // 设置 tshark 模块的日志级别
    const tsharkLogger = require('./tshark/manager').logger;
    if (tsharkLogger) {
        tsharkLogger.level = level;
    }

    // 设置 ip2region 模块的日志级别
    const ip2regionLogger = require('./utils/ip2region').logger;
    if (ip2regionLogger) {
        ip2regionLogger.level = level;
    }
}

// 打印所有数据包
function printAllPackets(manager) {
    const packets = manager.getAllPackets();
    for (const packet of packets) {
        console.log(JSON.stringify(packet, null, 2));
    }
}

// 解析命令行参数
program.parse(process.argv);

// 如果没有提供命令，显示帮助信息
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
