[package]
name = "rust_impl"
version = "0.1.0"
edition = "2021"
description = "Rust implementation of the packet analyzer"
authors = ["Your Name <<EMAIL>>"]

[dependencies]
# 命令行参数解析
clap = { version = "4.4", features = ["derive"] }
# 日志记录
log = "0.4"
env_logger = "0.10"
tracing-subscriber = "0.3"
# 序列化/反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
# 正则表达式
regex = "1.10"
# 异步运行时
tokio = { version = "1.35", features = ["full"] }
# HTTP 客户端
reqwest = { version = "0.11", features = ["json"] }
# 错误处理
anyhow = "1.0"
thiserror = "1.0"
# IP 地理位置查询
maxminddb = "0.23"
# IP2Region 库
xdb = { git = "https://github.com/lionsoul2014/ip2region.git", branch = "master" }
# 十六进制处理
hex = "0.4"
# 随机数生成
rand = "0.8"
# 网络数据包解析
pcap = "1.1"
etherparse = "0.13"
