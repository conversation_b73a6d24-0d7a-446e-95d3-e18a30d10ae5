stages:
  - build
  - test
  - deploy

variables:
  GIT_STRATEGY: clone

# Cache the Gradle wrapper and dependencies between builds
cache:
  paths:
    - .gradle/wrapper
    - .gradle/caches

# Build job
build:
  stage: build
  image: eclipse-temurin:17-jdk
  tags:
    - k3s
  before_script:
    # - apt-get update && apt-get install -y build-essential cmake g++ git curl
    - apt-get update && apt-get install -y build-essential g++
    - chmod +x ./gradlew
  script:
    - ./gradlew assemble
  artifacts:
    paths:
      - build/exe/main/debug/
      - build/exe/main/release/
    expire_in: 1 week

# Test job
test:
  stage: test
  image: eclipse-temurin:17-jdk
  tags:
    - k3s
  dependencies:
    - build
  before_script:
    # - apt-get update && apt-get install -y build-essential
    - chmod +x ./gradlew
  script:
    - cd build/exe/main/debug
    - ./main || echo "Test completed with exit code $?"
  artifacts:
    paths:
      - build/reports/
    expire_in: 1 week

# # Deploy job (example)
# deploy:
#   stage: deploy
#   image: eclipse-temurin:17-jdk
#   tags:
#     - k3s
#   dependencies:
#     - build
#   before_script:
#     - apt-get update && apt-get install -y curl
#   script:
#     - echo "Deploying application..."
#     # Add your deployment commands here
#     - echo "Application deployed!"
#   only:
#     - main  # Only run this job on the main branch
#   when: manual  # Make this job manual
