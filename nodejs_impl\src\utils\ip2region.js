const fs = require('fs');
const path = require('path');
const winston = require('winston');
const ip2region = require('ip2region');

// 配置日志
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
    ),
    transports: [
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.timestamp(),
                winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
            )
        })
    ]
});

// 全局单例
let ip2regionInstance = null;

/**
 * IP 地理位置查询工具
 */
class IP2Region {
    /**
     * 初始化 IP2Region 数据库
     * @param {string} xdbPath IP2Region 数据库路径
     * @returns {Promise<void>}
     */
    static async init(xdbPath) {
        try {
            // 检查文件是否存在
            // if (!fs.existsSync(xdbPath)) {
            //     throw new Error(`IP2Region database file not found: ${xdbPath}`);
            // }

            // 创建 IP2Region 实例
            ip2regionInstance = new ip2region.default();

            logger.info(`IP2Region database initialized successfully from ${xdbPath}`);
        } catch (error) {
            logger.error(`Failed to initialize IP2Region: ${error.message}`);
            throw error;
        }
    }

    /**
     * 获取 IP 地理位置
     * @param {string} ip IP 地址
     * @returns {string} IP 地理位置
     */
    static getIpLocation(ip) {
        if (!ip) {
            return '';
        }

        // 尝试解析 IP 地址
        try {
            // 检查是否为 IPv6 地址
            if (ip.includes(':')) {
                // 对于 IPv6 地址，直接返回空字符串
                logger.debug(`IPv6 address detected, returning empty result: ${ip}`);
                return '';
            }

            // 检查 IP2Region 实例是否已初始化
            if (!ip2regionInstance) {
                logger.error('IP2Region not initialized');
                return '';
            }

            // 使用 IP2Region 查询 IP 地址
            const result = ip2regionInstance.search(ip);

            // 检查结果格式
            if (typeof result === 'object' && result !== null) {
                // 对象格式: { country: '中国', province: '上海', city: '上海市', isp: '电信' }
                return IP2Region.parseLocationObject(result);
            } else if (typeof result === 'string') {
                // 字符串格式: "中国|0|上海|上海市|电信"
                return IP2Region.parseLocationString(result);
            } else {
                logger.error(`Unexpected result format: ${typeof result}`);
                return '';
            }
        } catch (error) {
            logger.error(`Failed to search IP location for ${ip}: ${error.message}`);
            return '';
        }
    }

    /**
     * 解析 IP 地理位置对象，格式化为所需格式
     *
     * 输入格式: { country: '中国', province: '上海', city: '上海市', isp: '电信' }
     * 输出格式: "中国-上海-上海市" (根据 C++ 版本的逻辑)
     * @param {Object} locationObj 地理位置对象
     * @returns {string} 格式化后的地理位置字符串
     */
    static parseLocationObject(locationObj) {
        // 检查是否为内网
        if (locationObj.country && locationObj.country.includes('内网')) {
            return '内网';
        }

        let result = '';

        // 国家/地区
        if (locationObj.country && locationObj.country !== '0') {
            result += locationObj.country;
        }

        // 省份
        if (locationObj.province && locationObj.province !== '0') {
            if (result) {
                result += '-';
            }
            result += locationObj.province;
        }

        // 城市
        if (locationObj.city && locationObj.city !== '0') {
            if (result) {
                result += '-';
            }
            result += locationObj.city;
        }

        return result;
    }

    /**
     * 解析 IP 地理位置字符串，格式化为所需格式
     *
     * 输入格式: "中国|0|上海|上海市|电信"
     * 输出格式: "中国-上海-上海市" (根据 C++ 版本的逻辑)
     * @param {string} input 原始地理位置字符串
     * @returns {string} 格式化后的地理位置字符串
     */
    static parseLocationString(input) {
        // 如果包含"内网"，直接返回"内网"
        if (input.includes('内网')) {
            return '内网';
        }

        // 按 | 分割字符串
        const tokens = input.split('|');

        // 如果分割后的数组长度小于 4，直接返回原始字符串
        if (tokens.length < 4) {
            return input;
        }

        let result = '';

        // 国家/地区 (tokens[0])
        if (tokens[0] !== '0') {
            result += tokens[0];
        }

        // 省份 (tokens[2])
        if (tokens[2] !== '0') {
            if (result) {
                result += '-';
            }
            result += tokens[2];
        }

        // 城市 (tokens[3])
        if (tokens[3] !== '0') {
            if (result) {
                result += '-';
            }
            result += tokens[3];
        }

        return result;
    }

    /**
     * 解析 IP 地理位置，格式化为所需格式 (兼容旧版本)
     * @param {string|Object} input 原始地理位置字符串或对象
     * @returns {string} 格式化后的地理位置字符串
     */
    static parseLocation(input) {
        if (typeof input === 'object' && input !== null) {
            return IP2Region.parseLocationObject(input);
        } else if (typeof input === 'string') {
            return IP2Region.parseLocationString(input);
        } else {
            logger.error(`Unexpected input format: ${typeof input}`);
            return '';
        }
    }

    /**
     * 清理资源
     */
    static uninit() {
        ip2regionInstance = null;
        logger.info('IP2Region database uninitialized');
    }
}

module.exports = IP2Region;
module.exports.logger = logger;
