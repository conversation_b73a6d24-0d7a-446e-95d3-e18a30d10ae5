use anyhow::{Context, Result};
use log::{debug, error, info};
use std::net::IpAddr;
use std::str::FromStr;
use xdb::{search_by_ip, searcher_init};

/// IP 地理位置查询工具
pub struct Ip2Region;

impl Ip2Region {
    /// 初始化 IP2Region 数据库
    pub fn init(xdb_path: &str) -> Result<()> {
        // 使用 xdb 库提供的全局初始化函数
        // 这个函数内部会处理线程安全和单例模式
        searcher_init(Some(xdb_path.to_owned()));

        info!("IP2Region database initialized successfully from {}", xdb_path);
        Ok(())
    }

    /// 获取 IP 地理位置
    pub fn get_ip_location(ip: &str) -> String {
        if ip.is_empty() {
            return String::new();
        }

        // 尝试解析 IP 地址
        match IpAddr::from_str(ip) {
            Ok(addr) => {
                // 检查是否为 IPv6 地址
                if let IpAddr::V6(_) = addr {
                    // 对于 IPv6 地址，直接返回空字符串
                    debug!("IPv6 address detected, returning empty result: {}", ip);
                    return String::new();
                }
            },
            Err(_) => {
                debug!("Invalid IP address: {}", ip);
                return String::new();
            }
        }

        // 只处理 IPv4 地址
        // 使用 xdb 库提供的全局查询函数
        match search_by_ip(ip) {
            Ok(region) => Self::parse_location(&region),
            Err(e) => {
                error!("Failed to search IP location for {}: {}", ip, e);
                String::new()
            }
        }
    }

    /// 解析 IP 地理位置字符串，格式化为所需格式
    ///
    /// 输入格式: "中国|0|上海|上海市|电信"
    /// 输出格式: "中国-上海-上海市" (根据 C++ 版本的逻辑)
    fn parse_location(input: &str) -> String {
        // 如果包含"内网"，直接返回"内网"
        if input.contains("内网") {
            return "内网".to_string();
        }

        // 按 | 分割字符串
        let tokens: Vec<&str> = input.split('|').collect();

        // 如果分割后的数组长度小于 4，直接返回原始字符串
        if tokens.len() < 4 {
            return input.to_string();
        }

        let mut result = String::new();

        // 国家/地区 (tokens[0])
        if tokens[0] != "0" {
            result.push_str(tokens[0]);
        }

        // 省份 (tokens[2])
        if tokens[2] != "0" {
            if !result.is_empty() {
                result.push('-');
            }
            result.push_str(tokens[2]);
        }

        // 城市 (tokens[3])
        if tokens[3] != "0" {
            if !result.is_empty() {
                result.push('-');
            }
            result.push_str(tokens[3]);
        }

        result
    }

    /// 清理资源
    /// 注意：xdb 库不需要显式清理资源，这个方法保留是为了兼容性
    pub fn uninit() {
        info!("IP2Region database uninitialized (no-op)");
    }
}
