/**
 * 表示一个网络数据包
 */
class Packet {
    constructor() {
        // 帧编号
        this.frameNumber = 0;
        // 时间戳
        this.time = '';
        // 源 MAC 地址
        this.srcMac = '';
        // 目标 MAC 地址
        this.dstMac = '';
        // 捕获长度
        this.capLen = 0;
        // 原始长度
        this.len = 0;
        // 源 IP 地址
        this.srcIp = '';
        // 源 IP 地理位置
        this.srcLocation = '';
        // 源端口
        this.srcPort = 0;
        // 目标 IP 地址
        this.dstIp = '';
        // 目标 IP 地理位置
        this.dstLocation = '';
        // 目标端口
        this.dstPort = 0;
        // 协议
        this.protocol = '';
        // 信息
        this.info = '';
        // 文件偏移
        this.fileOffset = 0;
    }
}

/**
 * PCAP 文件头
 */
class PcapHeader {
    constructor() {
        this.magicNumber = 0;
        this.versionMajor = 0;
        this.versionMinor = 0;
        this.thiszone = 0;
        this.sigfigs = 0;
        this.snaplen = 0;
        this.network = 0;
    }
}

/**
 * 数据包头
 */
class PacketHeader {
    constructor() {
        this.tsSec = 0;
        this.tsUsec = 0;
        this.caplen = 0;
        this.len = 0;
    }
}

/**
 * 网卡信息
 */
class AdapterInfo {
    constructor() {
        // 网卡 ID
        this.id = 0;
        // 网卡名称
        this.name = '';
        // 网卡备注
        this.remark = '';
    }
}

module.exports = {
    Packet,
    PcapHeader,
    PacketHeader,
    AdapterInfo
};
