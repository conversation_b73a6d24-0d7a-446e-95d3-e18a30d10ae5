package utils

import (
	"net"
	"strings"
	"sync"

	"github.com/lionsoul2014/ip2region/binding/golang/xdb"
	"github.com/sirupsen/logrus"
)

var (
	// 全局单例
	ip2regionInstance *IP2Region
	once              sync.Once
)

// IP2Region IP 地理位置查询工具
type IP2Region struct {
	searcher *xdb.Searcher
}

// InitIP2Region 初始化 IP2Region 数据库
func InitIP2Region(xdbPath string) error {
	var err error
	once.Do(func() {
		// 创建 IP2Region 实例
		ip2regionInstance = &IP2Region{}

		// 创建 XDB 搜索器
		ip2regionInstance.searcher, err = xdb.NewWithFileOnly(xdbPath)
		if err != nil {
			logrus.Errorf("Failed to create XDB searcher: %v", err)
			return
		}

		logrus.Infof("IP2Region database initialized successfully from %s", xdbPath)
	})

	return err
}

// GetIpLocation 获取 IP 地理位置
func GetIpLocation(ip string) string {
	if ip == "" {
		return ""
	}

	// 尝试解析 IP 地址
	ipAddr := net.ParseIP(ip)
	if ipAddr == nil {
		logrus.Debugf("Invalid IP address: %s", ip)
		return ""
	}

	// 检查是否为 IPv6 地址
	if ipAddr.To4() == nil {
		// 对于 IPv6 地址，直接返回空字符串
		logrus.Debugf("IPv6 address detected, returning empty result: %s", ip)
		return ""
	}

	// 检查 IP2Region 实例是否已初始化
	if ip2regionInstance == nil || ip2regionInstance.searcher == nil {
		logrus.Error("IP2Region not initialized")
		return ""
	}

	// 使用 XDB 搜索器查询 IP 地址
	region, err := ip2regionInstance.searcher.SearchByStr(ip)
	if err != nil {
		logrus.Errorf("Failed to search IP location for %s: %v", ip, err)
		return ""
	}

	return parseLocation(region)
}

// UninitIP2Region 清理资源
func UninitIP2Region() {
	if ip2regionInstance != nil && ip2regionInstance.searcher != nil {
		ip2regionInstance.searcher.Close()
		ip2regionInstance = nil
		logrus.Info("IP2Region database uninitialized")
	}
}

// parseLocation 解析 IP 地理位置字符串，格式化为所需格式
//
// 输入格式: "中国|0|上海|上海市|电信"
// 输出格式: "中国-上海-上海市" (根据 C++ 版本的逻辑)
func parseLocation(input string) string {
	// 如果包含"内网"，直接返回"内网"
	if strings.Contains(input, "内网") {
		return "内网"
	}

	// 按 | 分割字符串
	tokens := strings.Split(input, "|")

	// 如果分割后的数组长度小于 4，直接返回原始字符串
	if len(tokens) < 4 {
		return input
	}

	var result strings.Builder

	// 国家/地区 (tokens[0])
	if tokens[0] != "0" {
		result.WriteString(tokens[0])
	}

	// 省份 (tokens[2])
	if tokens[2] != "0" {
		if result.Len() > 0 {
			result.WriteString("-")
		}
		result.WriteString(tokens[2])
	}

	// 城市 (tokens[3])
	if tokens[3] != "0" {
		if result.Len() > 0 {
			result.WriteString("-")
		}
		result.WriteString(tokens[3])
	}

	return result.String()
}
