# 网络数据包分析工具 - Node.js 实现

这是一个使用 Node.js 重写的网络数据包分析工具，原项目是用 C++ 实现的。

## 功能

- 分析 PCAP 格式的网络数据包文件
- 提取数据包的详细信息（源/目标 IP、端口、协议等）
- 查询 IP 地理位置信息
- 列出系统网络适配器

## 依赖

- [TShark](https://www.wireshark.org/docs/man-pages/tshark.html) - Wireshark 的命令行版本
- [Node.js](https://nodejs.org/) - 14.0.0 或更高版本
- [ip2region](https://github.com/lionsoul2014/ip2region) - IP 地理位置数据库

## 安装

```bash
# 克隆仓库
git clone <repository-url>
cd <repository-name>/nodejs_impl

# 安装依赖
npm install
```

## 使用方法

### 分析数据包文件

```bash
# 使用 npm 脚本
npm run analyze -- -f path/to/capture.pcap

# 或者直接使用 Node.js
node src/index.js analyze -f path/to/capture.pcap
```

### 列出网络适配器

```bash
# 使用 npm 脚本
npm run list-adapters

# 或者直接使用 Node.js
node src/index.js list-adapters
```

### 其他选项

```bash
# 指定 TShark 路径
node src/index.js analyze -f path/to/capture.pcap -t /path/to/tshark

# 指定 IP2Region 数据库路径
node src/index.js analyze -f path/to/capture.pcap -i /path/to/ip2region.xdb

# 设置日志级别
node src/index.js analyze -f path/to/capture.pcap -l debug
```

## 项目结构

```
nodejs_impl/
├── src/
│   ├── models/
│   │   └── packet.js         # 数据模型
│   ├── tshark/
│   │   └── manager.js        # TShark 管理器
│   ├── utils/
│   │   └── ip2region.js      # IP 地理位置查询
│   └── index.js              # 程序入口
├── package.json              # 项目配置
└── README.md                 # 项目说明
```

## 与 C++ 版本的比较

Node.js 版本相比 C++ 版本有以下优势：

1. **开发效率** - JavaScript 的动态类型和高级抽象使开发更快速
2. **跨平台** - Node.js 程序可以在多种操作系统上运行，无需重新编译
3. **异步 I/O** - Node.js 的事件驱动、非阻塞 I/O 模型提高了性能
4. **丰富的生态系统** - npm 提供了大量可用的包和库
5. **易于部署** - 不需要编译步骤，可以直接运行源代码

## 许可证

[MIT](LICENSE)
