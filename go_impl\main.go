package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"runtime"

	"xuanyuan/go_impl/tshark"
	"xuanyuan/go_impl/utils"

	"github.com/sirupsen/logrus"
)

var (
	// 命令行参数
	analyzeFile   = flag.String("analyze", "", "分析数据包文件")
	listAdapters  = flag.Bool("list-adapters", false, "列出网络适配器")
	tsharkPath    = flag.String("tshark-path", "", "TShark 可执行文件路径")
	ip2regionPath = flag.String("ip2region-path", "ip2region.xdb", "IP2Region 数据库路径")
	logLevel      = flag.String("log-level", "info", "日志级别 (debug, info, warn, error)")
)

func init() {
	// 设置日志格式
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
	})

	// 设置日志输出
	logrus.SetOutput(os.Stdout)
}

func main() {
	// 解析命令行参数
	flag.Parse()

	// 设置日志级别
	setLogLevel(*logLevel)

	// 初始化 IP2Region
	if err := utils.InitIP2Region(*ip2regionPath); err != nil {
		logrus.Errorf("Failed to initialize IP2Region: %v", err)
		logrus.Errorf("IP 地理位置查询功能将不可用")
	}

	// 创建 TShark 管理器
	manager := tshark.NewTsharkManager()

	// 设置 TShark 路径
	if *tsharkPath != "" {
		manager.SetTsharkPath(*tsharkPath)
	} else {
		// 根据操作系统设置默认路径
		if runtime.GOOS == "windows" {
			manager.SetTsharkPath("C:\\Program Files\\Wireshark\\tshark.exe")
		}
	}

	// 处理命令
	if *analyzeFile != "" {
		// 分析数据包文件
		logrus.Infof("Analyzing file: %s", *analyzeFile)

		if err := manager.AnalysisFile(*analyzeFile); err != nil {
			logrus.Fatalf("Failed to analyze file: %v", err)
		}

		printAllPackets(manager)
		// 打印数据包数量
		logrus.Infof("Total packets: %d", manager.GetPacketCount())

		// 打印所有数据包
		// printAllPackets(manager)

		logrus.Info("Analysis completed successfully")
	} else if *listAdapters {
		// 列出网络适配器
		logrus.Info("Listing network adapters...")

		adapters, err := manager.GetNetworkAdapters()
		if err != nil {
			logrus.Fatalf("Failed to list network adapters: %v", err)
		}

		if len(adapters) == 0 {
			logrus.Info("No network adapters found")
		} else {
			for _, adapter := range adapters {
				logrus.Infof("ID: %d, Name: %s, Description: %s", adapter.ID, adapter.Name, adapter.Remark)
			}
		}
	} else {
		// 如果没有指定命令，显示帮助信息
		fmt.Println("请指定要执行的命令")
		printUsage()
	}

	// 清理资源
	manager.Cleanup()
}

// setLogLevel 设置日志级别
func setLogLevel(level string) {
	switch level {
	case "debug":
		logrus.SetLevel(logrus.DebugLevel)
	case "info":
		logrus.SetLevel(logrus.InfoLevel)
	case "warn":
		logrus.SetLevel(logrus.WarnLevel)
	case "error":
		logrus.SetLevel(logrus.ErrorLevel)
	default:
		logrus.SetLevel(logrus.InfoLevel)
	}
}

// printAllPackets 打印所有数据包
func printAllPackets(manager *tshark.TsharkManager) {
	packets := manager.GetAllPackets()
	for _, packet := range packets {
		data, err := json.MarshalIndent(packet, "", "  ")
		if err != nil {
			logrus.Errorf("Failed to marshal packet: %v", err)
			continue
		}
		fmt.Println(string(data))
	}
}

// printUsage 打印使用说明
func printUsage() {
	fmt.Printf("Usage: %s [options]\n", filepath.Base(os.Args[0]))
	fmt.Println("Options:")
	flag.PrintDefaults()
}
