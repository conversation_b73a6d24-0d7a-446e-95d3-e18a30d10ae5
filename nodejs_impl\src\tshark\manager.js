const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');
const winston = require('winston');
const { Packet, AdapterInfo } = require('../models/packet');
const IP2Region = require('../utils/ip2region');

// 配置日志
const logger = winston.createLogger({
    level: 'info',
    format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
    ),
    transports: [
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.timestamp(),
                winston.format.printf(info => `${info.timestamp} ${info.level}: ${info.message}`)
            )
        })
    ]
});

/**
 * TShark 管理器
 */
class TsharkManager {
    /**
     * 创建新的 TShark 管理器
     */
    constructor() {
        // TShark 可执行文件路径
        this.tsharkPath = 'tshark'; // 默认假设 tshark 在 PATH 中
        // 当前分析的文件路径
        this.currentFilePath = null;
        // 分析得到的所有数据包
        this.allPackets = new Map();
    }

    /**
     * 设置 TShark 路径
     * @param {string} path TShark 可执行文件路径
     */
    setTsharkPath(path) {
        this.tsharkPath = path;
    }

    /**
     * 分析数据包文件
     * @param {string} filePath 数据包文件路径
     * @returns {Promise<void>}
     */
    async analysisFile(filePath) {
        return new Promise((resolve, reject) => {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                return reject(new Error(`File not found: ${filePath}`));
            }

            // 构建 TShark 命令
            const tshark = spawn(this.tsharkPath, [
                '-r', filePath,
                '-T', 'fields',
                '-e', 'frame.number',
                '-e', 'frame.time_epoch',
                '-e', 'frame.len',
                '-e', 'frame.cap_len',
                '-e', 'eth.src',
                '-e', 'eth.dst',
                '-e', 'ip.src',
                '-e', 'ipv6.src',
                '-e', 'ip.dst',
                '-e', 'ipv6.dst',
                '-e', 'tcp.srcport',
                '-e', 'udp.srcport',
                '-e', 'tcp.dstport',
                '-e', 'udp.dstport',
                '-e', '_ws.col.Protocol',
                '-e', '_ws.col.Info'
            ]);

            // 当前处理的报文在文件中的偏移，第一个报文的偏移就是全局文件头24字节
            let fileOffset = 24; // sizeof(PcapHeader)

            // 清空之前的数据
            this.allPackets.clear();

            // 处理标准输出
            tshark.stdout.on('data', (data) => {
                const lines = data.toString().split('\n');

                for (const line of lines) {
                    if (!line.trim()) continue;

                    // 解析行
                    const packet = this.parseLine(line);
                    if (packet) {
                        // 计算当前报文的偏移，然后记录在 Packet 对象中
                        packet.fileOffset = fileOffset + 16; // sizeof(PacketHeader)

                        // 更新偏移游标
                        fileOffset = fileOffset + 16 + packet.capLen; // sizeof(PacketHeader) + cap_len

                        // 获取 IP 地理位置
                        packet.srcLocation = IP2Region.getIpLocation(packet.srcIp);
                        packet.dstLocation = IP2Region.getIpLocation(packet.dstIp);

                        // 保存数据包
                        this.allPackets.set(packet.frameNumber, packet);
                    }
                }
            });

            // 处理错误
            tshark.stderr.on('data', (data) => {
                logger.error(`TShark error: ${data}`);
            });

            // 处理进程结束
            tshark.on('close', (code) => {
                if (code !== 0) {
                    return reject(new Error(`TShark process exited with code ${code}`));
                }

                // 记录当前分析的文件路径
                this.currentFilePath = filePath;

                logger.info(`Analysis completed. Total packets: ${this.allPackets.size}`);
                resolve();
            });
        });
    }

    /**
     * 解析 TShark 输出的一行
     * @param {string} line TShark 输出的一行
     * @returns {Packet|null} 解析得到的数据包，如果解析失败则返回 null
     */
    parseLine(line) {
        const fields = line.split('\t');

        if (fields.length < 16) {
            logger.debug(`Invalid line format: ${line}`);
            return null;
        }

        const packet = new Packet();

        // 解析字段
        packet.frameNumber = parseInt(fields[0]) || 0;
        packet.time = fields[1];
        packet.len = parseInt(fields[2]) || 0;
        packet.capLen = parseInt(fields[3]) || 0;
        packet.srcMac = fields[4];
        packet.dstMac = fields[5];

        // IP 地址可能是 IPv4 或 IPv6
        packet.srcIp = fields[6] || fields[7];
        packet.dstIp = fields[8] || fields[9];

        // 端口可能是 TCP 或 UDP
        if (fields[10] || fields[11]) {
            packet.srcPort = parseInt(fields[10] || fields[11]) || 0;
        }

        if (fields[12] || fields[13]) {
            packet.dstPort = parseInt(fields[12] || fields[13]) || 0;
        }

        packet.protocol = fields[14];
        packet.info = fields[15];

        return packet;
    }

    /**
     * 获取数据包数量
     * @returns {number} 数据包数量
     */
    getPacketCount() {
        return this.allPackets.size;
    }

    /**
     * 获取指定编号的数据包
     * @param {number} frameNumber 帧编号
     * @returns {Packet|undefined} 数据包，如果不存在则返回 undefined
     */
    getPacket(frameNumber) {
        return this.allPackets.get(frameNumber);
    }

    /**
     * 获取所有数据包
     * @returns {Packet[]} 所有数据包
     */
    getAllPackets() {
        return Array.from(this.allPackets.values());
    }

    /**
     * 获取指定编号数据包的十六进制数据
     * @param {number} frameNumber 帧编号
     * @returns {Promise<Buffer>} 数据包的十六进制数据
     */
    async getPacketHexData(frameNumber) {
        // 检查当前是否有分析的文件
        if (!this.currentFilePath) {
            throw new Error('No file has been analyzed');
        }

        // 查找指定编号的数据包
        const packet = this.allPackets.get(frameNumber);
        if (!packet) {
            throw new Error(`Packet not found: ${frameNumber}`);
        }

        // 打开文件
        const fd = await fs.promises.open(this.currentFilePath, 'r');
        try {
            // 定位到数据包的位置
            await fd.seek(packet.fileOffset, 0);

            // 读取数据包内容
            const buffer = Buffer.alloc(packet.capLen);
            await fd.read(buffer, 0, packet.capLen, null);

            return buffer;
        } finally {
            await fd.close();
        }
    }

    /**
     * 枚举网卡列表
     * @returns {Promise<AdapterInfo[]>} 网卡列表
     */
    async getNetworkAdapters() {
        return new Promise((resolve, reject) => {
            // 需要过滤掉的虚拟网卡
            const specialInterfaces = new Set(['sshdump', 'ciscodump', 'udpdump', 'randpkt']);

            // 执行 tshark -D 命令
            const tshark = spawn(this.tsharkPath, ['-D']);

            let output = '';
            tshark.stdout.on('data', (data) => {
                output += data.toString();
            });

            tshark.stderr.on('data', (data) => {
                logger.error(`TShark error: ${data}`);
            });

            tshark.on('close', (code) => {
                if (code !== 0) {
                    return reject(new Error(`TShark process exited with code ${code}`));
                }

                // 打印原始输出以进行调试
                logger.debug(`TShark raw output:\n${output}`);

                const adapters = [];
                let index = 1;

                // 处理每一行
                const lines = output.split('\n');
                for (const line of lines) {
                    if (!line.trim()) continue;

                    logger.debug(`Processing line: ${line}`);

                    // 解析行
                    // 格式: "1. \Device\NPF_{3ECC51D8-DB1D-48D4-9E8A-638B9A7F1D66} (本地连接* 10)"
                    const parts = line.split('(');
                    if (parts.length < 2) {
                        logger.debug(`Skipping line: ${line} (no parenthesis found)`);
                        continue;
                    }

                    // 提取网卡名称和备注
                    const nameParts = parts[0].split('.');
                    if (nameParts.length < 2) {
                        logger.debug(`Skipping line: ${line} (no dot found)`);
                        continue;
                    }

                    const interfaceNumber = parseInt(nameParts[0].trim());
                    const interfaceName = nameParts[1].trim();

                    // 提取备注信息
                    let remark = parts[1].trim();

                    // 移除结尾的右括号
                    if (remark.endsWith(')')) {
                        remark = remark.substring(0, remark.length - 1).trim();
                    }

                    // 过滤特殊网卡
                    if (specialInterfaces.has(interfaceName)) {
                        logger.debug(`Skipping special interface: ${interfaceName}`);
                        continue;
                    }

                    const adapter = new AdapterInfo();
                    adapter.id = index++;
                    adapter.name = interfaceName;
                    adapter.remark = remark;

                    logger.debug(`Found adapter: ${adapter.name} with remark: ${adapter.remark}`);
                    adapters.push(adapter);
                }
                resolve(adapters);
            });
        });
    }

    /**
     * 清理资源
     */
    cleanup() {
        IP2Region.uninit();
    }
}

module.exports = TsharkManager;
module.exports.logger = logger;
