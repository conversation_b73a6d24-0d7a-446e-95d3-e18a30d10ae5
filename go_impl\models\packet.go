package models

// Packet 表示一个网络数据包
type Packet struct {
	// 帧编号
	FrameNumber uint32 `json:"frame_number"`
	// 时间戳
	Time string `json:"time"`
	// 源 MAC 地址
	SrcMAC string `json:"src_mac"`
	// 目标 MAC 地址
	DstMAC string `json:"dst_mac"`
	// 捕获长度
	CapLen uint32 `json:"cap_len"`
	// 原始长度
	Len uint32 `json:"len"`
	// 源 IP 地址
	SrcIP string `json:"src_ip"`
	// 源 IP 地理位置
	SrcLocation string `json:"src_location"`
	// 源端口
	SrcPort uint16 `json:"src_port"`
	// 目标 IP 地址
	DstIP string `json:"dst_ip"`
	// 目标 IP 地理位置
	DstLocation string `json:"dst_location"`
	// 目标端口
	DstPort uint16 `json:"dst_port"`
	// 协议
	Protocol string `json:"protocol"`
	// 信息
	Info string `json:"info"`
	// 文件偏移
	FileOffset uint32 `json:"file_offset"`
}

// NewPacket 创建一个新的数据包
func NewPacket() *Packet {
	return &Packet{}
}

// PcapHeader PCAP 文件头
type PcapHeader struct {
	MagicNumber uint32
	VersionMajor uint16
	VersionMinor uint16
	Thiszone int32
	Sigfigs uint32
	Snaplen uint32
	Network uint32
}

// PacketHeader 数据包头
type PacketHeader struct {
	TsSec uint32
	TsUsec uint32
	Caplen uint32
	Len uint32
}

// AdapterInfo 网卡信息
type AdapterInfo struct {
	// 网卡 ID
	ID int `json:"id"`
	// 网卡名称
	Name string `json:"name"`
	// 网卡备注
	Remark string `json:"remark"`
}

// NewAdapterInfo 创建一个新的网卡信息
func NewAdapterInfo() *AdapterInfo {
	return &AdapterInfo{}
}
