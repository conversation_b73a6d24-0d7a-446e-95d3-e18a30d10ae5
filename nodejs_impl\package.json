{"name": "nodejs_impl", "version": "1.0.0", "main": "src/index.js", "scripts": {"start": "node src/index.js", "analyze": "node src/index.js analyze", "list-adapters": "node src/index.js list-adapters", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "网络数据包分析工具 - Node.js 实现", "dependencies": {"child_process": "^1.0.2", "commander": "^13.1.0", "fs": "^0.0.1-security", "ip2region": "^2.3.0", "os": "^0.1.2", "path": "^0.12.7", "winston": "^3.17.0"}}