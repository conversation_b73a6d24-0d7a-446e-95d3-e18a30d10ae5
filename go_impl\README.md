# 网络数据包分析工具 - Go 实现

这是一个使用 Go 重写的网络数据包分析工具，原项目是用 C++ 实现的。

## 功能

- 分析 PCAP 格式的网络数据包文件
- 提取数据包的详细信息（源/目标 IP、端口、协议等）
- 查询 IP 地理位置信息
- 列出系统网络适配器

## 依赖

- [TShark](https://www.wireshark.org/docs/man-pages/tshark.html) - Wireshark 的命令行版本
- [Go](https://golang.org/) - 1.16 或更高版本
- [ip2region](https://github.com/lionsoul2014/ip2region) - IP 地理位置数据库

## 构建

```bash
# 克隆仓库
git clone <repository-url>
cd <repository-name>/go_impl

# 构建项目
go build -o xuanyuan ./cmd/xuanyuan
```

## 使用方法

### 分析数据包文件

```bash
# 分析 PCAP 文件
./xuanyuan -analyze path/to/capture.pcap
```

### 列出网络适配器

```bash
# 列出系统上的网络适配器
./xuanyuan -list-adapters
```

### 其他选项

```bash
# 指定 TShark 路径
./xuanyuan -tshark-path /path/to/tshark -analyze path/to/capture.pcap

# 指定 IP2Region 数据库路径
./xuanyuan -ip2region-path /path/to/ip2region.xdb -analyze path/to/capture.pcap

# 设置日志级别
./xuanyuan -log-level debug -analyze path/to/capture.pcap
```

## 项目结构

```
go_impl/
├── cmd/
│   └── xuanyuan/
│       └── main.go         # 程序入口
├── models/
│   └── packet.go           # 数据模型
├── tshark/
│   └── manager.go          # TShark 管理器
├── utils/
│   └── ip2region.go        # IP 地理位置查询
├── go.mod                  # Go 模块定义
├── go.sum                  # Go 模块校验和
└── README.md               # 项目说明
```

## 与 C++ 版本的比较

Go 版本相比 C++ 版本有以下优势：

1. **内存安全** - Go 的垃圾回收机制确保没有内存泄漏
2. **并发安全** - Go 的 goroutine 和 channel 提供了简单而强大的并发模型
3. **跨平台** - Go 程序可以轻松地在不同操作系统上构建和运行
4. **标准库丰富** - Go 的标准库提供了大量实用功能
5. **依赖管理** - Go 模块系统提供了简单的依赖管理

## 许可证

[MIT](LICENSE)
