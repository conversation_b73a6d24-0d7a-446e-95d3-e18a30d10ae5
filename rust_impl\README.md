# 网络数据包分析工具 - Rust 实现

这是一个使用 Rust 重写的网络数据包分析工具，原项目是用 C++ 实现的。

## 功能

- 分析 PCAP 格式的网络数据包文件
- 提取数据包的详细信息（源/目标 IP、端口、协议等）
- 查询 IP 地理位置信息
- 列出系统网络适配器

## 依赖

- [TShark](https://www.wireshark.org/docs/man-pages/tshark.html) - Wireshark 的命令行版本
- [Rust](https://www.rust-lang.org/) - 1.70.0 或更高版本

## 构建

```bash
# 克隆仓库
git clone <repository-url>
cd <repository-name>/rust_impl

# 构建项目
cargo build --release
```

## 使用方法

### 分析数据包文件

```bash
# 分析 PCAP 文件
cargo run -- analyze --file path/to/capture.pcap
```

### 列出网络适配器

```bash
# 列出系统上的网络适配器
cargo run -- list-adapters
```

## 项目结构

```
rust_impl/
├── src/
│   ├── models/           # 数据模型
│   │   ├── mod.rs
│   │   └── packet.rs     # 数据包相关结构体
│   ├── tshark/           # TShark 相关功能
│   │   ├── mod.rs
│   │   └── manager.rs    # TShark 管理器
│   ├── utils/            # 工具函数
│   │   ├── mod.rs
│   │   └── ip2region.rs  # IP 地理位置查询
│   └── main.rs           # 程序入口
├── Cargo.toml            # 项目配置
└── README.md             # 项目说明
```

## 与 C++ 版本的比较

Rust 版本相比 C++ 版本有以下优势：

1. **内存安全** - Rust 的所有权系统确保没有内存泄漏和悬垂指针
2. **并发安全** - Rust 的类型系统和借用检查器在编译时防止数据竞争
3. **错误处理** - 使用 Result 类型进行更清晰的错误处理
4. **包管理** - Cargo 提供了简单的依赖管理
5. **现代语言特性** - 模式匹配、trait、闭包等现代语言特性

## 许可证

[MIT](LICENSE)
