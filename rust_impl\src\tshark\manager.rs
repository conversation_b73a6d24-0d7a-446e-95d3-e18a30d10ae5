use crate::models::packet::{AdapterInfo, Packet};
use crate::utils::ip2region::Ip2Region;
use anyhow::{Context, Result};
use log::{debug, error, info};
use regex::Regex;
use std::collections::HashMap;
use std::io::{BufRead, BufReader};
use std::path::Path;
use std::process::{Command, Stdio};
use std::sync::Arc;

/// TShark 管理器
pub struct TsharkManager {
    /// TShark 可执行文件路径
    tshark_path: String,
    /// 当前分析的文件路径
    current_file_path: Option<String>,
    /// 分析得到的所有数据包
    all_packets: HashMap<u32, Arc<Packet>>,
}

impl TsharkManager {
    /// 创建新的 TShark 管理器
    pub fn new() -> Self {
        // 初始化 IP2Region
        if let Err(e) = Ip2Region::init("ip2region.xdb") {
            error!("Failed to initialize IP2Region: {}", e);
            // 继续执行，但 IP 地理位置查询功能将不可用
        } else {
            info!("IP2Region initialized successfully");
        }

        Self {
            tshark_path: "tshark".to_string(), // 默认假设 tshark 在 PATH 中
            current_file_path: None,
            all_packets: HashMap::new(),
        }
    }

    /// 设置 TShark 路径
    pub fn set_tshark_path(&mut self, path: &str) {
        self.tshark_path = path.to_string();
    }

    /// 分析数据包文件
    pub fn analysis_file(&mut self, file_path: &str) -> Result<()> {
        let path = Path::new(file_path);
        if !path.exists() {
            return Err(anyhow::anyhow!("File not found: {}", file_path));
        }

        // 构建 TShark 命令
        let mut cmd = Command::new(&self.tshark_path);
        cmd.args([
            "-r", file_path,
            "-T", "fields",
            "-e", "frame.number",
            "-e", "frame.time_epoch",
            "-e", "frame.len",
            "-e", "frame.cap_len",
            "-e", "eth.src",
            "-e", "eth.dst",
            "-e", "ip.src",
            "-e", "ipv6.src",
            "-e", "ip.dst",
            "-e", "ipv6.dst",
            "-e", "tcp.srcport",
            "-e", "udp.srcport",
            "-e", "tcp.dstport",
            "-e", "udp.dstport",
            "-e", "_ws.col.Protocol",
            "-e", "_ws.col.Info",
        ]);

        // 执行命令
        let process = cmd.stdout(Stdio::piped())
            .spawn()
            .context("Failed to execute tshark command")?;

        let reader = BufReader::new(process.stdout.unwrap());

        // 当前处理的报文在文件中的偏移，第一个报文的偏移就是全局文件头24字节
        let mut file_offset = 24; // sizeof(PcapHeader)

        // 清空之前的数据
        self.all_packets.clear();

        // 处理每一行输出
        for line in reader.lines() {
            let line = line.context("Failed to read line from tshark output")?;

            // 解析行
            if let Some(packet) = self.parse_line(&line) {
                // 计算当前报文的偏移，然后记录在 Packet 对象中
                let mut packet = packet;
                packet.file_offset = file_offset + 16; // sizeof(PacketHeader)

                // 更新偏移游标
                file_offset = file_offset + 16 + packet.cap_len; // sizeof(PacketHeader) + cap_len

                // 获取 IP 地理位置
                packet.src_location = Ip2Region::get_ip_location(&packet.src_ip);
                packet.dst_location = Ip2Region::get_ip_location(&packet.dst_ip);

                // 保存数据包
                self.all_packets.insert(packet.frame_number, Arc::new(packet));
            }
        }

        // 记录当前分析的文件路径
        self.current_file_path = Some(file_path.to_string());

        info!("Analysis completed. Total packets: {}", self.all_packets.len());
        Ok(())
    }

    /// 解析 TShark 输出的一行
    fn parse_line(&self, line: &str) -> Option<Packet> {
        let fields: Vec<&str> = line.split('\t').collect();

        if fields.len() < 16 {
            debug!("Invalid line format: {}", line);
            return None;
        }

        let mut packet = Packet::default();

        // 解析字段
        packet.frame_number = fields[0].parse().unwrap_or(0);
        packet.time = fields[1].to_string();
        packet.len = fields[2].parse().unwrap_or(0);
        packet.cap_len = fields[3].parse().unwrap_or(0);
        packet.src_mac = fields[4].to_string();
        packet.dst_mac = fields[5].to_string();

        // IP 地址可能是 IPv4 或 IPv6
        packet.src_ip = if !fields[6].is_empty() {
            fields[6].to_string()
        } else {
            fields[7].to_string()
        };

        packet.dst_ip = if !fields[8].is_empty() {
            fields[8].to_string()
        } else {
            fields[9].to_string()
        };

        // 端口可能是 TCP 或 UDP
        if !fields[10].is_empty() || !fields[11].is_empty() {
            packet.src_port = if !fields[10].is_empty() {
                fields[10].parse().unwrap_or(0)
            } else {
                fields[11].parse().unwrap_or(0)
            };
        }

        if !fields[12].is_empty() || !fields[13].is_empty() {
            packet.dst_port = if !fields[12].is_empty() {
                fields[12].parse().unwrap_or(0)
            } else {
                fields[13].parse().unwrap_or(0)
            };
        }

        packet.protocol = fields[14].to_string();
        packet.info = fields[15].to_string();

        Some(packet)
    }

    /// 打印所有数据包的信息
    pub fn print_all_packets(&self) {
        for (_, packet_arc) in &self.all_packets {
            // 解引用 Arc 获取 Packet 的引用
            let packet: &Packet = &*packet_arc;
            let json = serde_json::to_string_pretty(packet).unwrap_or_else(|_| "Error serializing packet".to_string());
            info!("{}", json);
        }
    }

    /// 输出包总数
    pub fn print_packet_count(&self) {
        info!("Total packets: {}", self.all_packets.len());
    }

    /// 获取指定编号数据包的十六进制数据
    pub fn get_packet_hex_data(&self, frame_number: u32) -> Result<Vec<u8>> {
        // 检查当前是否有分析的文件
        let file_path = match &self.current_file_path {
            Some(path) => path,
            None => return Err(anyhow::anyhow!("No file has been analyzed")),
        };

        // 查找指定编号的数据包
        let packet = match self.all_packets.get(&frame_number) {
            Some(p) => p,
            None => return Err(anyhow::anyhow!("Packet not found: {}", frame_number)),
        };

        // 打开文件
        let mut file = std::fs::File::open(file_path)?;

        // 定位到数据包的位置
        std::io::Seek::seek(&mut file, std::io::SeekFrom::Start(packet.file_offset as u64))?;

        // 读取数据包内容
        let mut buffer = vec![0u8; packet.cap_len as usize];
        std::io::Read::read_exact(&mut file, &mut buffer)?;

        Ok(buffer)
    }

    /// 枚举网卡列表
    pub fn get_network_adapters(&self) -> Result<Vec<AdapterInfo>> {
        // 需要过滤掉的虚拟网卡
        let special_interfaces = vec!["sshdump", "ciscodump", "udpdump", "etwdump", "randpkt"];

        // 执行 tshark -D 命令
        let output = Command::new(&self.tshark_path)
            .arg("-D")
            .output()
            .context("Failed to execute tshark -D command")?;

        if !output.status.success() {
            let error = String::from_utf8_lossy(&output.stderr);
            return Err(anyhow::anyhow!("tshark command failed: {}", error));
        }

        let output_str = String::from_utf8_lossy(&output.stdout);

        // 创建正则表达式模式
        let pattern = Regex::new(r"^\d+\.\s+(\S+)(?:.*?\(([^)]*)\))?.*$")?;

        let mut adapters = Vec::new();
        let mut index = 1;

        // 处理每一行
        for line in output_str.lines() {
            debug!("Processing line: {}", line);

            if let Some(captures) = pattern.captures(line) {
                if let Some(interface_name) = captures.get(1) {
                    let name = interface_name.as_str().to_string();

                    // 过滤特殊网卡
                    if special_interfaces.contains(&name.as_str()) {
                        continue;
                    }

                    let mut adapter = AdapterInfo {
                        id: index,
                        name,
                        remark: String::new(),
                    };

                    // 提取备注信息
                    if let Some(remark) = captures.get(2) {
                        adapter.remark = remark.as_str().to_string();
                        debug!("Found adapter: {} with remark: {}", adapter.name, adapter.remark);
                    } else {
                        debug!("Found adapter: {} (no remark)", adapter.name);
                    }

                    adapters.push(adapter);
                    index += 1;
                }
            }
        }

        Ok(adapters)
    }

    /// 清理资源
    pub fn cleanup(&self) {
        Ip2Region::uninit();
    }
}

impl Drop for TsharkManager {
    fn drop(&mut self) {
        self.cleanup();
    }
}
