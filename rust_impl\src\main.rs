mod models;
mod tshark;
mod utils;

use anyhow::Result;
use clap::{Parser, Subcommand};
use env_logger::Env;
use log::{error, info};
use std::path::PathBuf;
use tshark::manager::TsharkManager;

/// 网络数据包分析工具 - Rust 实现
#[derive(Parser)]
#[command(author, version, about, long_about = None)]
struct Cli {
    /// 子命令
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// 分析数据包文件
    Analyze {
        /// 数据包文件路径
        #[arg(short, long)]
        file: PathBuf,
    },
    /// 列出网络适配器
    ListAdapters,
}

fn main() -> Result<()> {
    // 初始化日志
    env_logger::init_from_env(Env::default().default_filter_or("info"));

    // 解析命令行参数
    let cli = Cli::parse();

    // 创建 TShark 管理器
    let mut manager = TsharkManager::new();

    // 设置 TShark 路径
    #[cfg(windows)]
    manager.set_tshark_path(r#"C:\Program Files\Wireshark\tshark.exe"#);

    // 检查 IP2Region 数据库文件是否存在，如果不存在则提示用户
    let xdb_path = "ip2region.xdb";
    if !std::path::Path::new(xdb_path).exists() {
        error!("IP2Region 数据库文件 {} 不存在", xdb_path);
        error!("请从 https://github.com/lionsoul2014/ip2region/tree/master/data 下载数据库文件");
        error!("并将其放置在当前目录下");
    }

    // 处理命令
    match &cli.command {
        Commands::Analyze { file } => {
            info!("Analyzing file: {}", file.display());

            if let Err(e) = manager.analysis_file(file.to_str().unwrap_or("")) {
                error!("Failed to analyze file: {}", e);
                return Err(e);
            }

            manager.print_all_packets();
            // 打印数据包数量
            manager.print_packet_count();

            // 打印所有数据包
            // manager.print_all_packets();

            info!("Analysis completed successfully");
        }
        Commands::ListAdapters => {
            info!("Listing network adapters...");

            match manager.get_network_adapters() {
                Ok(adapters) => {
                    if adapters.is_empty() {
                        info!("No network adapters found");
                    } else {
                        for adapter in adapters {
                            info!(
                                "ID: {}, Name: {}, Description: {}",
                                adapter.id, adapter.name, adapter.remark
                            );
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to list network adapters: {}", e);
                    return Err(e);
                }
            }
        }
    }

    Ok(())
}
