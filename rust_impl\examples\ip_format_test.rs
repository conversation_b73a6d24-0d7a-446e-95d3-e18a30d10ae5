use rust_impl::utils::ip2region::Ip2Region;
use std::env;

fn main() {
    // 配置日志
    env_logger::init();

    // 获取 IP2Region 数据库路径
    let xdb_path = env::args().nth(1).unwrap_or_else(|| "./ip2region.xdb".to_string());

    // 初始化 IP2Region
    if let Err(e) = Ip2Region::init(&xdb_path) {
        eprintln!("Failed to initialize IP2Region: {}", e);
        return;
    }

    // 测试 IP 地址列表
    let test_ips = vec![
        // IPv4 地址
        "*******",       // Cloudflare DNS
        "*******",       // Google DNS
        "***************", // 114 DNS (中国)
        "*********",     // 阿里 DNS
        "************",  // 腾讯 DNS
        "127.0.0.1",     // 本地回环地址
        "***********",   // 内网地址

        // IPv6 地址 (应该返回空结果)
        "2606:4700:4700::1111",  // Cloudflare DNS IPv6
        "2001:4860:4860::8888",  // Google DNS IPv6
        "2400:3200::1",          // 阿里 DNS IPv6
        "2402:4e00::",           // 腾讯 DNS IPv6
        "::1",                   // 本地回环地址 IPv6
        "fe80::1",               // 链路本地地址
    ];

    // 测试每个 IP 地址
    for ip in test_ips {
        let location = Ip2Region::get_ip_location(ip);
        println!("IP: {}, Location: {}", ip, location);
    }

    // 注意：我们不能直接测试特定格式的解析，因为 parse_location 是私有方法
    // 但我们可以通过查看实际 IP 地址的解析结果来验证格式化逻辑
    println!("\n格式化后的结果应该符合 '国家-省份-城市' 的格式，例如 '中国-上海-上海市'");
}
