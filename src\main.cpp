#include "tshark_manager.h"
#include "loguru/loguru.hpp"
#include <iostream>
#include <string>
#include <fstream>

// 保存JSON字符串到文件的工具函数
bool saveJsonToFile(const std::string& jsonContent, const std::string& filename) {
    std::ofstream file(filename);
    if (!file.is_open()) {
        std::cerr << "错误：无法创建文件 " << filename << std::endl;
        return false;
    }

    file << jsonContent;
    file.close();

    if (file.fail()) {
        std::cerr << "错误：写入文件失败 " << filename << std::endl;
        return false;
    }

    return true;
}

int main(int argc, char* argv[]) {
    loguru::init(argc, argv);
    loguru::add_file("logs.txt", loguru::Append, loguru::Verbosity_MAX);

    TsharkManager tsharkManager;

    // 第一步：提示用户输入PCAP文件路径
    std::string pcapFilePath;
    std::cout << "请输入要分析的PCAP文件路径: ";
    std::getline(std::cin, pcapFilePath);

    // 去除路径两端的引号（如果有的话）
    if (pcapFilePath.front() == '"' && pcapFilePath.back() == '"') {
        pcapFilePath = pcapFilePath.substr(1, pcapFilePath.length() - 2);
    }

    // 分析PCAP文件
    std::cout << "正在分析PCAP文件，请稍候..." << std::endl;
    if (!tsharkManager.analysisFile(pcapFilePath)) {
        std::cerr << "错误：无法分析PCAP文件 " << pcapFilePath << std::endl;
        return 1;
    }

    // 获取数据包总数
    tsharkManager.printPacketCount();

    // 第二步：循环提示用户输入数据包编号
    while (true) {
        std::string input;
        std::cout << "\n请输入要获取详情的数据包编号（1-" << tsharkManager.getPacketCount() << "），或输入 'q' 退出: ";
        std::getline(std::cin, input);

        // 检查是否要退出
        if (input == "q" || input == "Q") {
            std::cout << "程序退出。" << std::endl;
            break;
        }

        // 尝试转换为数字
        try {
            uint32_t packetNumber = std::stoul(input);

            // 验证数据包编号范围
            if (packetNumber < 1 || packetNumber > tsharkManager.getPacketCount()) {
                std::cerr << "错误：数据包编号超出范围，请输入 1-" << tsharkManager.getPacketCount() << " 之间的数字。" << std::endl;
                continue;
            }

            // 获取数据包详情
            std::string packetDetailJson;
            std::cout << "正在获取第 " << packetNumber << " 个数据包的详情..." << std::endl;

            if (tsharkManager.getPacketDetailInfo(packetNumber, packetDetailJson)) {
                // 保存到文件
                std::string filename = std::to_string(packetNumber) + ".json";
                if (saveJsonToFile(packetDetailJson, filename)) {
                    std::cout << "成功！数据包详情已保存到文件: " << filename << std::endl;
                } else {
                    std::cerr << "错误：保存文件失败。" << std::endl;
                }
            } else {
                std::cerr << "错误：无法获取第 " << packetNumber << " 个数据包的详情。" << std::endl;
            }

        } catch (const std::exception& e) {
            std::cerr << "错误：输入的不是有效数字，请重新输入。" << std::endl;
        }
    }

    return 0;
}