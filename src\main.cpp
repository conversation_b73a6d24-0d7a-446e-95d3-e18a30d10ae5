#include "tshark_manager.h"
#include "loguru/loguru.hpp"

int main(int argc, char* argv[]) {
    loguru::init(argc, argv);
    loguru::add_file("logs.txt", loguru::Append, loguru::Verbosity_MAX);

    TsharkManager tsharkManager;
    tsharkManager.analysisFile("C:\\Users\\<USER>\\capture.pcap");

    // tsharkManager.printAllPackets();
    // tsharkManager.printPacketCount();
    // std::vector<AdapterInfo> adaptors = tsharkManager.getNetworkAdapters();
    // for (auto item : adaptors) {
    //     LOG_F(INFO, "网卡[%d]: name[%s] remark[%s]", item.id, item.name.c_str(), item.remark.c_str());
    // }

    // tsharkManager.startCapture("\\Device\\NPF_{2447AFFD-C7E0-49B1-A852-56D081C2A9ED}");
    // // 主线程进入命令等待停止抓包
    // std::string input;
    // while (true) {
    //     std::cout << "请输入q退出抓包: ";
    //     std::cin >> input;
    //     if (input == "q") {
    //         tsharkManager.stopCapture();
    //         break;
    //     }
    // }

    // // 打印所有捕获到的数据包信息
    // tsharkManager.printAllPackets();

    
    // // 启动监控
    // tsharkManager.startMonitorAdaptersFlowTrend();

    // // 睡眠10秒，等待监控网卡数据
    // std::this_thread::sleep_for(std::chrono::seconds(10));

    // // 读取监控到的数据
    // std::map<std::string, std::map<long, long>> trendData;
    // tsharkManager.getAdaptersFlowTrendData(trendData);

    // // 停止监控
    // tsharkManager.stopMonitorAdaptersFlowTrend();

    // // 把获取到的数据打印输出
    // rapidjson::Document resDoc;
    // rapidjson::Document::AllocatorType& allocator = resDoc.GetAllocator();
    // resDoc.SetObject();
    // rapidjson::Value dataObject(rapidjson::kObjectType);
    // for (const auto &adaptorItem : trendData) {
    //     rapidjson::Value adaptorDataList(rapidjson::kArrayType);
    //     for (const auto &timeItem : adaptorItem.second) {
    //         rapidjson::Value timeObj(rapidjson::kObjectType);
    //         timeObj.AddMember("time", (unsigned int)timeItem.first, allocator);
    //         timeObj.AddMember("bytes", (unsigned int)timeItem.second, allocator);
    //         adaptorDataList.PushBack(timeObj, allocator);
    //     }

    //     dataObject.AddMember(rapidjson::StringRef(adaptorItem.first.c_str()), adaptorDataList, allocator);
    // }

    // resDoc.AddMember("data", dataObject, allocator);

    // // 序列化为 JSON 字符串
    // rapidjson::StringBuffer buffer;
    // rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
    // resDoc.Accept(writer);

    // LOG_F(INFO, "网卡流量监控数据: %s", buffer.GetString());
    std::string packetDetailJson;
    tsharkManager.getPacketDetailInfo(10, packetDetailJson);

    LOG_F(INFO, "第10个包的协议树为：%s", packetDetailJson.c_str());
    return 0;
}