FROM eclipse-temurin:17-jdk

# Install required C++ build tools
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    g++ \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set up working directory
WORKDIR /app

# Copy the project files
COPY . .

# Make gradlew executable
RUN chmod +x ./gradlew

# Build the project
RUN ./gradlew assemble

# Set the default command
CMD ["./build/exe/main/debug/main"]
