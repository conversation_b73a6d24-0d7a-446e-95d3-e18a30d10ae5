plugins {
    id 'cpp-application'
    id 'base'
}

application {
    // 设置主可执行文件名称
    baseName = 'main'
}

// 配置 C++ 编译器选项
tasks.withType(CppCompile).configureEach {
    // 添加编译器标志，区分 Windows 和 Linux/Mac
    if (org.gradle.internal.os.OperatingSystem.current().isWindows()) {
        compilerArgs.add('/EHsc')  // Windows: 启用 C++ 异常处理
        compilerArgs.add('/std:c++17')  // Windows: 使用 C++17 标准
    } else {
        compilerArgs.add('-std=c++17')  // Linux/Mac: 使用 C++17 标准
    }

    // 添加包含路径
    includes.from('src')
    includes.from('lib')
}

// 配置链接器选项
tasks.withType(LinkExecutable).configureEach {
}

// 配置源文件
application {
    // 指定源文件目录
    privateHeaders.from fileTree('src').include('**/*.h')
    privateHeaders.from fileTree('lib').include('**/*.h')

    // 自动获取所有 C++ 源文件
    source.from fileTree('src').include('**/*.cpp', '**/*.cc', '**/*.c')
    source.from fileTree('lib').include('**/*.cpp', '**/*.cc', '**/*.c')
}

// 复制 ip2region.xdb 文件到输出目录
tasks.register('copyIp2RegionDataDebug', Copy) {
    from 'lib/ip2region/ip2region.xdb'
    into "${buildDir}/exe/main/debug"
}

tasks.register('copyIp2RegionDataRelease', Copy) {
    from 'lib/ip2region/ip2region.xdb'
    into "${buildDir}/exe/main/release"
}

// 确保在构建后复制数据文件
tasks.named('assemble').configure {
    dependsOn 'copyIp2RegionDataDebug', 'copyIp2RegionDataRelease'
}

// 确保 install 任务依赖于相应的 copy 任务
tasks.withType(org.gradle.nativeplatform.tasks.InstallExecutable).configureEach { task ->
    if (task.name == 'installDebug') {
        task.dependsOn 'copyIp2RegionDataDebug'
    } else if (task.name == 'installRelease') {
        task.dependsOn 'copyIp2RegionDataRelease'
    }
}

// 确保 stripSymbols 任务依赖于相应的 copy 任务
tasks.withType(org.gradle.nativeplatform.tasks.StripSymbols).configureEach { task ->
    if (task.name == 'stripSymbolsRelease') {
        task.dependsOn 'copyIp2RegionDataRelease'
    }
}

// 添加运行任务
tasks.register('runApp', Exec) {
    dependsOn 'assemble'
    workingDir "${buildDir}/exe/main/debug"
    commandLine "${buildDir}/exe/main/debug/main.exe"
}

// 添加 Gradle Wrapper 任务
wrapper {
    gradleVersion = '8.5'
    distributionType = 'bin'
}
